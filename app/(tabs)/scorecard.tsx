import * as React from 'react';
import { StyleSheet, ScrollView, View, TouchableOpacity } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import ParallaxScrollView from '@/components/ParallaxScrollView';

interface HoleData {
  holeNumber: number;
  score: number;
  clubs: string[];
}

export default function ExploreScreen() {
  const [holes, setHoles] = React.useState<HoleData[]>(
    Array.from({ length: 18 }, (_, i) => ({
      holeNumber: i + 1,
      score: 0,
      clubs: [],
    }))
  );

  const clubOptions = [
    'Driver', '3W', '5W', '4i', '5i', '6i', '7i', '8i', '9i', 'PW', 'SW', 'Putter'
  ];

  const addClub = (holeIndex: number, club: string) => {
    setHoles(currentHoles => {
      const newHoles = [...currentHoles];
      if (!newHoles[holeIndex].clubs.includes(club)) {
        newHoles[holeIndex] = {
          ...newHoles[holeIndex],
          clubs: [...newHoles[holeIndex].clubs, club],
        };
      }
      return newHoles;
    });
  };

  const removeClub = (holeIndex: number, club: string) => {
    setHoles(currentHoles => {
      const newHoles = [...currentHoles];
      newHoles[holeIndex] = {
        ...newHoles[holeIndex],
        clubs: newHoles[holeIndex].clubs.filter(c => c !== club),
      };
      return newHoles;
    });
  };

  const adjustScore = (holeIndex: number, increment: boolean) => {
    setHoles(currentHoles => {
      const newHoles = [...currentHoles];
      newHoles[holeIndex] = {
        ...newHoles[holeIndex],
        score: Math.max(0, newHoles[holeIndex].score + (increment ? 1 : -1)),
      };
      return newHoles;
    });
  };

  const totalScore = holes.reduce((sum, hole) => sum + hole.score, 0);

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#808080"
          name="flag.fill"
          style={styles.headerImage}
        />
      }>
      <ThemedView style={styles.headerContainer}>
        <ThemedText type="title">Golf Scorecard</ThemedText>
        <ThemedText type="defaultSemiBold" style={styles.totalScore}>
          Total Score: {totalScore}
        </ThemedText>
      </ThemedView>

      {holes.map((hole, index) => (
        <ThemedView key={`hole-${hole.holeNumber}`} style={styles.holeContainer}>
          <View style={styles.holeHeader}>
            <ThemedText type="defaultSemiBold">Hole {hole.holeNumber}</ThemedText>
            <View style={styles.scoreContainer}>
              <TouchableOpacity
                onPress={() => adjustScore(index, false)}
                style={styles.scoreButton}>
                <ThemedText style={styles.scoreButtonText}>-</ThemedText>
              </TouchableOpacity>
              <ThemedText style={styles.score}>{hole.score}</ThemedText>
              <TouchableOpacity
                onPress={() => adjustScore(index, true)}
                style={styles.scoreButton}>
                <ThemedText style={styles.scoreButtonText}>+</ThemedText>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.clubsContainer}>
            <ThemedText type="defaultSemiBold" style={styles.clubsLabel}>
              Clubs Used:
            </ThemedText>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.clubsList}>
              {clubOptions.map(club => (
                <TouchableOpacity
                  key={`${hole.holeNumber}-${club}`}
                  onPress={() =>
                    hole.clubs.includes(club)
                      ? removeClub(index, club)
                      : addClub(index, club)
                  }
                  style={[
                    styles.clubButton,
                    hole.clubs.includes(club) && styles.clubButtonSelected,
                  ]}>
                  <ThemedText
                    style={[
                      styles.clubButtonText,
                      hole.clubs.includes(club) && styles.clubButtonTextSelected,
                    ]}>
                    {club}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </ThemedView>
      ))}
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  headerContainer: {
    marginBottom: 20,
    alignItems: 'center',
    padding: 16,
  },
  totalScore: {
    marginTop: 8,
    fontSize: 18,
  },
  holeContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  holeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#ddd',
    marginHorizontal: 8,
    minWidth: 32,
    alignItems: 'center',
  },
  scoreButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  score: {
    fontSize: 18,
    minWidth: 24,
    textAlign: 'center',
  },
  clubsContainer: {
    marginTop: 8,
  },
  clubsLabel: {
    marginBottom: 8,
  },
  clubsList: {
    flexDirection: 'row',
  },
  clubButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  clubButtonSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#0056b3',
  },
  clubButtonText: {
    color: '#333',
  },
  clubButtonTextSelected: {
    color: '#fff',
  },
});
